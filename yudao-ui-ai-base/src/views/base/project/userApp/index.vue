<script lang="ts" setup>
import { columns, searchFormSchema } from './user.data'
import { userAppPage } from '@/api/common'
import { useI18n } from '@/hooks/web/useI18n'
import { BasicTable, TableAction, useTable } from '@/components/Table'
import { IconEnum } from '@/enums/appEnum'

defineOptions({ name: 'UserPackage' })

const { t } = useI18n()

const [registerTable] = useTable({
  title: '用户APP',
  api: userAppPage,
  formConfig: { labelWidth: 120, schemas: searchFormSchema },
  columns,
  useSearchForm: true,
  showTableSetting: true,
  actionColumn: {
    width: 250,
    title: t('common.action'),
    dataIndex: 'action',
    fixed: 'right',
  }
})


</script>
<template>
  <div>
    <BasicTable @register="registerTable">
    </BasicTable>
  </div>
</template>