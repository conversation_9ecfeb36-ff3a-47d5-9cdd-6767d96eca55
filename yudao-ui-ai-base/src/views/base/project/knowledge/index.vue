<script lang="ts" setup>
import { columns, searchFormSchema } from './user.data'
import { knowledgePage,downloadFile } from '@/api/common'
import { useI18n } from '@/hooks/web/useI18n'
import { BasicTable, TableAction, useTable } from '@/components/Table'
import { useMessage } from '@/hooks/web/useMessage'
import { IconEnum } from '@/enums/appEnum'
import { useRouter,useRoute } from 'vue-router'
const route = useRoute()

defineOptions({ name: 'Knowledge' })

const router = useRouter()
const { t } = useI18n()
const { createMessage } = useMessage()

// 预览文件
function handlePreview(record: Recordable) {
  if (record.cdnSignedUrl) {
    window.open(record.cdnSignedUrl, '_blank')
  } else {
    createMessage.warning('预览链接不存在')
  }
}

const handleList = (record: Recordable)=>{
  router.push(`/project/knowledge/page?pid=${record.id}`)
}

// 下载文件
async function handleDownload(record: Recordable) {
  try {
    await downloadFile({id: record.id, userName: record.userName})
    createMessage.success('下载成功')
  } catch (error) {
    console.error('下载失败:', error)
    createMessage.error('下载失败')
  }
}
searchFormSchema[3].defaultValue = route.query.pid
const [registerTable] = useTable({
  title: '知识库',
  api: knowledgePage,
  formConfig: { labelWidth: 120, schemas: searchFormSchema },
  columns,
  useSearchForm: true,
  showTableSetting: true,
  actionColumn: {
    width: 250,
    title: t('common.action'),
    dataIndex: 'action',
    fixed: 'right',
  }
})

</script>
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              { icon: IconEnum.PREVIEW, label: '预览', onClick: handlePreview.bind(null, record),
                ifShow: () => {
                  return (record.type === 2)
                }
              },
              { icon: IconEnum.PREVIEW, label: '查看', onClick: handleList.bind(null, record),
                ifShow: () => {
                  return (record.type === 1)
                }
              },
              { icon: IconEnum.DOWNLOAD, label: '下载', onClick: handleDownload.bind(null, record),
                ifShow: () => {
                  return (record.type === 1 )
                }
               },
            ]"
          />
        </template>
      </template>
    </BasicTable>
  </div>
</template>