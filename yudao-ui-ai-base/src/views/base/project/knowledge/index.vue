<script lang="ts" setup>
import { columns, searchFormSchema } from './user.data'
import { knowledgePage } from '@/api/common'
import { useI18n } from '@/hooks/web/useI18n'
import { BasicTable, TableAction, useTable } from '@/components/Table'
import { useMessage } from '@/hooks/web/useMessage'
import { IconEnum } from '@/enums/appEnum'

defineOptions({ name: 'Knowledge' })

const { t } = useI18n()
const { createMessage } = useMessage()

const [registerTable] = useTable({
  title: '知识库',
  api: knowledgePage,
  formConfig: { labelWidth: 120, schemas: searchFormSchema },
  columns,
  useSearchForm: true,
  showTableSetting: true,
  actionColumn: {
    width: 250,
    title: t('common.action'),
    dataIndex: 'action',
    fixed: 'right',
  }
})

</script>
<template>
  <div>
    <BasicTable @register="registerTable">
    </BasicTable>
  </div>
</template>