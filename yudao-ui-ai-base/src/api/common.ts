import { defHttp } from '@/utils/http/axios'


// 国际化价格配置
export function createProduct(params) {
  return defHttp.post({ url: '/appLangs/createProduct', params })
}
// 订阅链接日志
export function subLogPage(params) {
  return defHttp.get({ url: '/medsciUsers/subLogPage', params })
}
// 订单列表
export function subOrderPage(params) {
  return defHttp.get({ url: '/medsciUsers/subOrderPage', params })
}
// 事件日志
export function eventLogPage(params) {
  return defHttp.get({ url: '/medsciUsers/eventLogPage', params })
}

// 事件日志
export function statistic(params) {
  return defHttp.get({ url: '/infra/api-access-log/statistic', params })
}

// 更新价格 
export function updatePriceId(params) {
  return defHttp.post({ url: '/appLangs/updatePriceId', params })
}

// 初始化国际产品及价格
export function initProject(params) {
  return defHttp.post({ url: '/appLangs/initProject', params })
}

// 中文订阅套餐
export function userPackagePage(params) {
  return defHttp.get({ url: '/medsciUsers/userPackagePage', params })
}

// 用户应用
export function userAppPage(params) {
  return defHttp.get({ url: '/medsciUsers/userAppPage', params })
}

// 知识库
export function knowledgePage(params) {
  return defHttp.get({ url: '/medsciUsers/knowledgePage', params })
}

// 下载
export function downloadFile(params) {
  return defHttp.get({ url: '/medsciUsers/downloadFile', params })
}

// 导入套餐
export function importPackage(params) {
  return defHttp.post({ url: '/medsciUsers/importPackage', params })
}
