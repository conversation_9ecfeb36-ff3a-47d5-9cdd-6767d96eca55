import type { AppRouteModule, AppRouteRecordRaw } from '@/router/types'

import { PAGE_NOT_FOUND_ROUTE, REDIRECT_ROUTE } from '@/router/routes/basic'

import { PageEnum } from '@/enums/pageEnum'
import { t } from '@/hooks/web/useI18n'
import { LAYOUT } from '@/router/constant'

// import.meta.glob() 直接引入所有的模块 Vite 独有的功能
const modules = import.meta.glob('./modules/**/*.ts', { eager: true })
const routeModuleList: AppRouteModule[] = []

// 加入到路由集合中
Object.keys(modules).forEach((key) => {
  const mod = (modules as Recordable)[key].default || {}
  const modList = Array.isArray(mod) ? [...mod] : [mod]
  routeModuleList.push(...modList)
})

export const asyncRoutes = [PAGE_NOT_FOUND_ROUTE, ...routeModuleList]

// 根路由
export const RootRoute: AppRouteRecordRaw = {
  path: '/',
  name: 'Root',
  redirect: PageEnum.BASE_HOME,
  meta: {
    title: 'Root',
  },
}

export const LoginRoute: AppRouteRecordRaw = {
  path: '/login',
  name: 'Login',
  component: () => import('@/views/base/login/Login.vue'),
  meta: {
    title: t('routes.basic.login'),
  },
}

export const SSORoute: AppRouteRecordRaw = {
  path: '/sso',
  name: 'SSO',
  component: () => import('@/views/base/login/sso.vue'),
  meta: {
    title: t('routes.basic.sso'),
  },
}

export const ProfileRoute: AppRouteRecordRaw = {
  path: '/profile',
  component: LAYOUT,
  name: 'Profile',
  meta: {
    title: t('routes.basic.profile'),
    hidden: true,
  },
  children: [
    {
      path: 'index',
      component: () => import('@/views/base/profile/index.vue'),
      name: 'UserProfile',
      meta: {
        canTo: true,
        hidden: true,
        noTagsView: false,
        icon: 'ant-design:user-outlined',
        title: t('routes.basic.profile'),
      },
    },
    {
      path: 'notify-message',
      component: () => import('@/views/system/notify/my/index.vue'),
      name: 'MyNotifyMessage',
      meta: {
        canTo: true,
        hidden: true,
        noTagsView: false,
        icon: 'ant-design:bell-outlined',
        title: t('routes.basic.notifyMessage'),
      },
    },
  ],
}


export const CodegenRoute: AppRouteRecordRaw = {
  path: '/codegen',
  component: LAYOUT,
  name: 'CodegenEdit',
  meta: {
    title: '修改生成配置',
    hidden: true,
  },
  children: [
    {
      path: 'editTable',
      component: () => import('@/views/infra/codegen/EditTable.vue'),
      name: 'EditTable',
      meta: {
        canTo: true,
        hidden: true,
        noTagsView: false,
        icon: 'ant-design:edit-outlined',
        title: '修改生成配置',
        activeMenu: 'infra/codegen/index',
      },
    },
  ],
}

export const JobLogRoute: AppRouteRecordRaw = {
  path: '/job',
  component: LAYOUT,
  name: 'JobL',
  meta: {
    title: '调度日志',
    hidden: true,
  },
  children: [
    {
      path: 'job-log',
      component: () => import('@/views/infra/job/logger/index.vue'),
      name: 'InfraJobLog',
      meta: {
        canTo: true,
        hidden: true,
        noTagsView: false,
        icon: 'ant-design:bar-chart-outlined',
        title: '调度日志',
        activeMenu: 'infra/job/index',
      },
    },
  ],
}

export const PayRoute: AppRouteRecordRaw = {
  path: '/pay',
  component: LAYOUT,
  name: 'pay',
  meta: {
    title: '收银台',
    hidden: true,
  },
  children: [
    {
      path: 'cashier',
      component: () => import('@/views/pay/cashier/index.vue'),
      name: 'PayCashier',
      meta: {
        canTo: true,
        hidden: true,
        noTagsView: false,
        icon: 'ant-design:pay-circle-outlined',
        title: '收银台',
        activeMenu: 'pay/order/index',
      },
    },
  ],
}

export const BpmRoute: AppRouteRecordRaw = {
  path: '/bpm',
  component: LAYOUT,
  name: 'bpm',
  meta: {
    title: '工作流',
    hidden: true,
  },
  children: [
    {
      path: '/manager/form/edit',
      component: () => import('@/views/bpm/form/editor/index.vue'),
      name: 'BpmFormEditor',
      meta: {
        canTo: true,
        hidden: true,
        noTagsView: false,
        icon: 'ant-design:edit-outlined',
        title: '设计流程表单',
        activeMenu: '/bpm/manager/form',
      },
    },
    {
      path: '/manager/model/edit',
      component: () => import('@/views/bpm/model/editor/index.vue'),
      name: 'BpmModelEditor',
      meta: {
        canTo: true,
        hidden: true,
        noTagsView: false,
        icon: 'ant-design:edit-outlined',
        title: '设计流程',
        activeMenu: '/bpm/manager/model',
      },
    },
    {
      path: '/manager/definition',
      component: () => import('@/views/bpm/definition/index.vue'),
      name: 'BpmProcessDefinition',
      meta: {
        canTo: true,
        hidden: true,
        noTagsView: false,
        icon: 'ant-design:edit-outlined',
        title: '流程定义',
        activeMenu: '/bpm/manager/model',
      },
    },
    // {
    //   path: '/manager/task-assign-rule',
    //   component: () => import('@/views/bpm/taskAssignRule/index.vue'),
    //   name: 'BpmTaskAssignRuleList',
    //   meta: {
    //     canTo: true,
    //     hidden: true,
    //     noTagsView: false,
    //     icon: 'ant-design:edit-outlined',
    //     title: '任务分配规则',
    //   },
    // },
    {
      path: '/process-instance/create',
      component: () => import('@/views/bpm/processInstance/create/index.vue'),
      name: 'BpmProcessInstanceCreate',
      meta: {
        canTo: true,
        hidden: true,
        noTagsView: false,
        icon: 'ant-design:edit-outlined',
        title: '发起流程',
        activeMenu: 'bpm/processInstance/create',
      },
    },
    {
      path: '/process-instance/detail',
      component: () => import('@/views/bpm/processInstance/detail/index.vue'),
      name: 'BpmProcessInstanceDetail',
      meta: {
        canTo: true,
        hidden: true,
        noTagsView: false,
        icon: 'ant-design:edit-outlined',
        title: '流程详情',
        activeMenu: 'bpm/processInstance/detail',
      },
    },
    {
      path: '/bpm/oa/leave/create',
      component: () => import('@/views/bpm/oa/leave/create.vue'),
      name: 'OALeaveCreate',
      meta: {
        canTo: true,
        hidden: true,
        noTagsView: false,
        icon: 'ant-design:edit-outlined',
        title: '发起 OA 请假',
        activeMenu: 'bpm/oa/leave',
      },
    },
    {
      path: '/process-instance/detail',
      component: () => import('@/views/bpm/oa/leave/detail.vue'),
      name: 'OALeaveDetail',
      meta: {
        canTo: true,
        hidden: true,
        noTagsView: false,
        icon: 'ant-design:edit-outlined',
        title: '查看 OA 请假',
        activeMenu: 'bpm/oa/leave',
      },
    },
  ],
}

export const Prompt: AppRouteRecordRaw = {
  path: '/prompt',
  component: LAYOUT,
  name: 'Prompt',
  meta: {
    title: 'Prompt基础库',
    hidden: true,
  },
  children: [
    {
      path: 'dictionary',
      component: () => import('@/views/base/prompt/dictionary/index.vue'),
      name: 'dictionaryPage',
      meta: {
        canTo: true,
        hidden: true,
        noTagsView: false,
        // icon: 'ant-design:edit-outlined',
        title: '功能字典',
        activeMenu: 'dictionary',
      },
    },
    {
      path: 'theme',
      component: () => import('@/views/base/prompt/dialogueTheme/index.vue'),
      name: 'dialogueTheme',
      meta: {
        canTo: true,
        hidden: true,
        noTagsView: false,
        // icon: 'ant-design:edit-outlined',
        title: 'AI对话主题',
        activeMenu: 'theme',
      }
    },
    {
      path: 'theme/create',
      component: import('@/views/base/prompt/dialogueTheme/components/addDialogueTheme.vue'),
      name: 'dialogueThemeCreate',
      meta: {
        canTo: true,
        hidden: true,
        noTagsView: false,
        // icon: 'ant-design:edit-outlined',
        title: '添加对话主题',
        activeMenu: 'theme/create',
      },
    },
    {
       path: 'theme/edit',
       component: import('@/views/base/prompt/dialogueTheme/components/addDialogueTheme.vue'),
      name: 'dialogueThemeEdit',
      meta: {
        canTo: true,
        hidden: true,
        noTagsView: false,
        // icon: 'ant-design:edit-outlined',
        title: '编辑对话主题',
        activeMenu: 'theme/edit',
      },
    },
    {
       path: 'theme/detail',
       component: import('@/views/base/prompt/dialogueTheme/components/addDialogueTheme.vue'),
      name: 'dialogueThemeDetail',
      meta: {
        canTo: true,
        hidden: true,
        noTagsView: false,
        // icon: 'ant-design:edit-outlined',
        title: '对话主题详情',
        activeMenu: 'theme/detail',
      },
    },
    {
      path: 'riskControl',
      component: () => import('@/views/base/prompt/riskControl/index.vue'),
      name: 'riskControlPage',
      meta: {
        canTo: true,
        hidden: true,
        noTagsView: false,
        // icon: 'ant-design:edit-outlined',
        title: 'AI风控管理',
        activeMenu: 'riskControl',
      }
    },
    {
      path: 'riskControl/detail',
      component: () => import('@/views/base/prompt/riskControl/components/riskDetail.vue'),
      name: 'riskControlDetail',
      meta: {
        canTo: true,
        hidden: true,
        noTagsView: false,
        // icon: 'ant-design:edit-outlined',
        title: '查看AI风控管理',
        activeMenu: 'riskControl/detail',
      }
    },
    {
      path: 'ai',
      component: () => import('@/views/base/prompt/aiManagement/index.vue'),
      name: 'aiManagement',
      meta: {
        canTo: true,
        hidden: true,
        noTagsView: false,
        // icon: 'ant-design:edit-outlined',
        title: 'AI应用管理',
        activeMenu: 'ai',
      },
    },
    {
      path: 'nation',
      component: () => import('@/views/base/prompt/aiNation/index.vue'),
      name: 'aiNation',
      meta: {
        canTo: true,
        hidden: true,
        noTagsView: false,
        // icon: 'ant-design:edit-outlined',
        title: '国际化配置',
        activeMenu: 'ai',
      },
    },
  ],
}

export const Project: AppRouteRecordRaw = {
  path: '/project',
  component: LAYOUT,
  name: 'Project',
  meta: {
    title: '项目管理',
    hidden: true,
  },
  children: [
    {
      path: 'manage',
      component: () => import('@/views/base/project/projectMagage/index.vue'),
      name: 'ProjectManage',
      meta: {
        canTo: true,
        hidden: true,
        noTagsView: false,
        // icon: 'ant-design:edit-outlined',
        title: '项目管理',
        activeMenu: 'manage',
      },
    },
     {
      path: 'manage/create',
      component: () => import('@/views/base/project/projectMagage/components/addProject.vue'),
      name: 'projectMagageCreate',
      meta: {
        canTo: true,
        hidden: true,
        noTagsView: false,
        // icon: 'ant-design:edit-outlined',
        title: '添加项目',
        activeMenu: 'manage',
      },
    },
    {
       path: 'manage/edit',
       component:  () => import('@/views/base/project/projectMagage/components/addProject.vue'),
      name: 'projectMagageEdit',
      meta: {
        canTo: true,
        hidden: true,
        noTagsView: false,
        // icon: 'ant-design:edit-outlined',
        title: '编辑项目',
        activeMenu: 'manage/edit',
      }
    },
    {
      path: 'manage/detail',
      component: () => import('@/views/base/project/projectMagage/components/addProject.vue'),
      name: 'projectMagageDetail',
      meta: {
        canTo: true,
        hidden: true,
        noTagsView: false,
        // icon: 'ant-design:edit-outlined',
        title: '项目详情',
        activeMenu: 'manage/detail',
      }
    },
     {
      path: 'dialogue',
      component: () => import('@/views/base/project/aiDialogue/index.vue'),
      name: 'DialoguePage',
      meta: {
        canTo: true,
        hidden: true,
        noTagsView: false,
        // icon: 'ant-design:edit-outlined',
        title: 'AI对话库',
        activeMenu: 'dialogue',
      },
    },
    {
      path: 'subscriber',
      component: () => import('@/views/base/project/msUsers/index.vue'),
      name: 'MsUsers',
      meta: {
        canTo: true,
        hidden: true,
        noTagsView: false,
        // icon: 'ant-design:edit-outlined',
        title: '主站用户',
      },
    },
    {
      path: 'subscription/log',
      component: () => import('@/views/base/project/subscription/index.vue'),
      name: 'Subscription',
      meta: {
        canTo: true,
        hidden: true,
        noTagsView: false,
        // icon: 'ant-design:edit-outlined',
        title: '订阅链接日志',
      },
    },
    {
      path: 'order/log',
      component: () => import('@/views/base/project/orderLog/index.vue'),
      name: 'orderLog',
      meta: {
        canTo: true,
        hidden: true,
        noTagsView: false,
        // icon: 'ant-design:edit-outlined',
        title: '订单日志',
      },
    },
    {
      path: 'event/log',
      component: () => import('@/views/base/project/eventLog/index.vue'),
      name: 'EventLog',
      meta: {
        canTo: true,
        hidden: true,
        noTagsView: false,
        // icon: 'ant-design:edit-outlined',
        title: '事件日志',
      },
    },
    {
      path: 'user/package',
      component: () => import('@/views/base/project/userPackage/index.vue'),
      name: 'UserPackage',
      meta: {
        canTo: true,
        hidden: true,
        noTagsView: false,
        // icon: 'ant-design:edit-outlined',
        title: '中文订阅套餐',
      },
    },
    {
      path: 'user/app',
      component: () => import('@/views/base/project/userApp/index.vue'),
      name: 'userApp',
      meta: {
        canTo: true,
        hidden: true,
        noTagsView: false,
        // icon: 'ant-design:edit-outlined',
        title: '用户APP',
      },
    },
    {
      path: 'knowledge',
      component: () => import('@/views/base/project/knowledge/index.vue'),
      name: 'Knowledge',
      meta: {
        canTo: true,
        hidden: true,
        noTagsView: false,
        // icon: 'ant-design:edit-outlined',
        title: '知识库',
      },
    },
  ],
}

export const Operations: AppRouteRecordRaw = {
  path: '/operations',
  component: LAYOUT,
  name: 'Operations',
  meta: {
    title: '运营管理',
    hidden: true,
  },
  children: [
    {
      path: 'dify/statistics/messages',
      component: () => import('@/views/base/project/difyStatisticsMessages/index.vue'),
      name: 'difyStatisticsMessages',
      meta: {
        canTo: true,
        hidden: true,
        noTagsView: false,
        title: 'Dify日志',
      },
    },
    {
      path: 'ai/statistics',
      component: () => import('@/views/base/project/aiStatistics/index.vue'),
      name: 'aiStatistics',
      meta: {
        canTo: true,
        hidden: true,
        noTagsView: false,
        title: 'AI首页统计',
      },
    },
    {
      path: 'data/dashboard',
      component: () => import('@/views/base/operations/DataDashboard/index.vue'),
      name: 'DataDashboard',
      meta: {
        canTo: true,
        hidden: true,
        noTagsView: false,
        title: '数据看板',
      },
    },
    {
      path: 'ai/log',
      component: () => import('@/views/base/project/aiLog/index.vue'),
      name: 'aiLog',
      meta: {
        canTo: true,
        hidden: true,
        noTagsView: false,
        title: 'AI应用访问统计',
      },
    },
  ]
}

export const AiModel: AppRouteRecordRaw = {
  path: '/ai',
  component: LAYOUT,
  name: 'AIModel',
  meta: {
    title: 'AI模型管理',
    hidden: true,
  },
  children: [
    {
      path: 'assistants',
      component: () => import('@/views/base/ai/assistants/index.vue'),
      name: 'aiAssistants',
      meta: {
        canTo: true,
        hidden: true,
        noTagsView: false,
        // icon: 'ant-design:edit-outlined',
        title: 'AI助理',
        activeMenu: 'assistants',
      },
    },
     {
      path: 'assistants/create',
      component: () => import('@/views/base/ai/assistants/components/addAssistant.vue'),
      name: 'aiAssistantsCreate',
      meta: {
        canTo: true,
        hidden: true,
        noTagsView: false,
        // icon: 'ant-design:edit-outlined',
        title: '添加AI助理',
        activeMenu: 'assistants/create',
      },
    },
    {
      path: 'assistants/edit',
      component: () => import('@/views/base/ai/assistants/components/addAssistant.vue'),
      name: 'aiAssistantsEdit',
      meta: {
        canTo: true,
        hidden: true,
        noTagsView: false,
        // icon: 'ant-design:edit-outlined',
        title: '编辑AI助理',
        activeMenu: 'assistants/edit',
      }
    },
    {
      path: 'assistants/detail',
      component: () => import('@/views/base/ai/assistants/components/addAssistant.vue'),
      name: 'aiAssistantsDetail',
      meta: {
        canTo: true,
        hidden: true,
        noTagsView: false,
        // icon: 'ant-design:edit-outlined',
        title: 'AI助理详情',
        activeMenu: 'assistants/detail',
      }
    },
    {
      path: 'model',
      component: () => import('@/views/base/ai/model/index.vue'),
      name: 'aiModel',
      meta: {
        canTo: true,
        hidden: true,
        noTagsView: false,
        // icon: 'ant-design:edit-outlined',
        title: 'AI模型',
        activeMenu: 'model',
      },
    }
  ],
}
export const Tutorial: AppRouteRecordRaw = {
  path: '/tutorial',
  component: LAYOUT,
  name: 'Tutorial',
  meta: {
    title: '教程',
    hidden: true,
  },
  children: [
    {
      path: '/tutorial',
      component: () => import('@/views/base/prompt/dictionary/components/Tutorial.vue'),
      name: 'TutorialPage',
      meta: {
        canTo: true,
        hidden: true,
        noTagsView: false,
        // icon: 'ant-design:edit-outlined',
        title: '教程',
        activeMenu: '/tutorial',
      },
    }
  ],
}
// Basic routing without permission
// 未经许可的基本路由
export const basicRoutes = [
  LoginRoute,
  SSORoute,
  RootRoute,
  ProfileRoute,
  CodegenRoute,
  JobLogRoute,
  PayRoute,
  BpmRoute,
  REDIRECT_ROUTE,
  PAGE_NOT_FOUND_ROUTE,
  Prompt,
  Project,
  Operations,
  AiModel,
  Tutorial
]
