package cn.iocoder.yudao.aiBase.service;

import cn.iocoder.yudao.aiBase.dto.param.KnowledgeParam;
import cn.iocoder.yudao.aiBase.dto.request.FileUploadRequest;
import cn.iocoder.yudao.aiBase.dto.response.KnowledgeResponse;
import cn.iocoder.yudao.aiBase.entity.AiAppKnowledgeFile;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import cn.iocoder.yudao.aiBase.config.BaseIService;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface AiAppKnowledgeFileService extends BaseIService<AiAppKnowledgeFile, KnowledgeParam> {

    public final String DIR_NAME = "DataScore";
    public final String DataScore_TEST = "a361754b-afb4-4022-bfa9-950a04a7d760";
    public final String DataScore_PROD = "c42715df-6f09-424e-a058-d2f2c582507d";

    /**
     * 查询知识文件列表
     * @param reqVO 查询参数
     * @return 知识文件列表
     */
    List<AiAppKnowledgeFile> selectList(KnowledgeParam reqVO);

    /**
     * 根据参数分页查询知识文件列表
     * @param reqVO 请求参数
     * @return 知识文件分页结果
     */
    PageResult<AiAppKnowledgeFile> selectPage(KnowledgeParam reqVO);

    String getAppId();

    List<KnowledgeResponse> getKnowledgeByDir(KnowledgeParam reqVO);

    Boolean updateFileName(Integer id, String fileName);

    KnowledgeResponse create(FileUploadRequest reqVO, HttpServletRequest request);

    Boolean delete(Integer id);

    List<KnowledgeResponse> getFilePath(Integer id);

    /**
     * 直接上传文件到参赛作品目录
     * @param file 上传的文件
     * @param request HTTP请求
     * @return 上传结果
     */
    KnowledgeResponse uploadToCompetitionDir(MultipartFile file, HttpServletRequest request);

    /**
     * 下载知识库目录下的所有文件（打包成zip）
     * @param id 知识库目录ID
     * @param userName 用户名
     * @param response HTTP响应
     */
    void downloadFile(Integer id, String userName, HttpServletResponse response);

}