package cn.iocoder.yudao.aiBase.service.impl;

import cn.iocoder.yudao.aiBase.config.BaseConstant;
import cn.iocoder.yudao.aiBase.config.ErrorCodeConstants;
import cn.iocoder.yudao.aiBase.dto.request.FileUploadRequest;
import cn.iocoder.yudao.aiBase.dto.request.dify.ChatMessagesRequest;
import cn.iocoder.yudao.aiBase.dto.request.dify.FilesUploadRequest;
import cn.iocoder.yudao.aiBase.dto.response.KnowledgeResponse;
import cn.iocoder.yudao.aiBase.entity.AiAppKnowledgeFile;
import cn.iocoder.yudao.aiBase.entity.MedsciUsers;
import cn.iocoder.yudao.aiBase.mapper.AiAppKnowledgeFileMapper;
import cn.iocoder.yudao.aiBase.service.*;
import cn.iocoder.yudao.aiBase.config.DBConstant;
import cn.iocoder.yudao.aiBase.dto.param.KnowledgeParam;
import cn.iocoder.yudao.aiBase.util.CommonUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.security.core.LoginUser;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.http.HttpUtil;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;

@Service
@DS(DBConstant.AiBase)
@Slf4j
public class AiAppKnowledgeFileServiceImpl extends ServiceImpl<AiAppKnowledgeFileMapper, AiAppKnowledgeFile> implements AiAppKnowledgeFileService {

    @Autowired
    private OpenApiService openApiService;

    @Autowired
    private YudaoSystemService yudaoSystemService;

    @Autowired
    private ApiTokensService apiTokensService;

    @Autowired
    private MedsciUsersService medsciUsersService;

    @Value("${spring.profiles.active}")
    private String active;

    @Override
    public List<AiAppKnowledgeFile> selectList(KnowledgeParam reqVO) {
        return baseMapper.selectList(reqVO);
    }

    @Override
    public PageResult<AiAppKnowledgeFile> selectPage(KnowledgeParam reqVO) {
        return baseMapper.selectPage(reqVO);
    }

    @Override
    public String getAppId() {
        return BaseConstant.PROD_STR.equals(active) ? DataScore_PROD : DataScore_TEST;
    }

    @Override
    public List<KnowledgeResponse> getKnowledgeByDir(KnowledgeParam reqVO) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        reqVO.setSocialUserId(loginUser.getId());
        reqVO.setSocialType(loginUser.getUserType());

        List<AiAppKnowledgeFile> files = selectList(reqVO);
        if (BaseConstant.ZERO.equals(reqVO.getPid())) {
            if (files.isEmpty()) {
                initDataScore();
                files = selectList(reqVO);
            } else {
                Boolean hasDataScore = files.stream().anyMatch(file -> file.getFileName().equals(DIR_NAME));
                if (!hasDataScore) {
                    initDataScore();
                    files = selectList(reqVO);
                }
            }
        }

        List<KnowledgeResponse> res = new ArrayList<>();
        for (AiAppKnowledgeFile file : files) {
            res.add(toBean(file));
        }
        return res;
    }

    private void initDataScore() {
        FileUploadRequest req = FileUploadRequest.builder()
                .type(BaseConstant.ONE)
                .pid(BaseConstant.ZERO)
                .fileName(DIR_NAME)
                .build();
        create(req, null);
        req.setFileName("参赛作品");
        create(req, null);
    }

    public void updateCdn(AiAppKnowledgeFile file) {
        Long time = System.currentTimeMillis()/1000;
        if (BaseConstant.TWO.equals(file.getType()) && time >= file.getT() - BaseConstant.SIXTY) {
            try {
                JSONObject res = openApiService.getCosPresignedUrl(file.getCosFilePath());
                log.info("获取签名地址失败，结果: {}", res);
                if (res.getString("data") != null) {
                    file.setCdnSignedUrl(res.getString("data"));
                    file.setT(System.currentTimeMillis()/BaseConstant.KILO + 1800);
                    baseMapper.updateById(file);
                } else {
                    throw exception(ErrorCodeConstants.ERROR_5038);
                }
            } catch (Exception e) {
                log.error("获取签名地址失败，错误信息: {}", e);
                throw exception(ErrorCodeConstants.ERROR_5038);
            }
        }
    }
    

    
    @Override
    public Boolean updateFileName(Integer id, String fileName) {
        if (id == null || StringUtils.isEmpty(fileName)) {
            throw exception(ErrorCodeConstants.BAD_REQUEST);
        }
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        AiAppKnowledgeFile file = getById(id);
        if (file == null) {
            return true;
        }

        if (file.getSocialUserId().equals(loginUser.getId()) && file.getSocialType().equals(loginUser.getUserType())) {
            file.setFileName(fileName);
            file.setUpdatedAt(LocalDateTime.now());
            return updateById(file);
        }

        throw exception(ErrorCodeConstants.FORBIDDEN);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public KnowledgeResponse create(FileUploadRequest reqVO, HttpServletRequest request) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        Integer level = 0;
        if (reqVO.getPid() > 0) {
            AiAppKnowledgeFile parent = getById(reqVO.getPid());
            if (parent == null || BaseConstant.TWO.equals(parent.getType()) ||
                    !parent.getSocialUserId().equals(loginUser.getId()) || !parent.getSocialType().equals(loginUser.getUserType())) {
                throw exception(ErrorCodeConstants.FORBIDDEN);
            }
            level = parent.getLevel() + 1;
            if (BaseConstant.ONE.equals(reqVO.getType()) && level >= BaseConstant.TEN) {
                throw exception(ErrorCodeConstants.ERROR_5056);
            }
        }
//        if (BaseConstant.ZERO.equals(reqVO.getPid()) && BaseConstant.ONE.equals(reqVO.getType()) && DIR_NAME.equals(reqVO.getFileName()) ) {
//            throw exception(ErrorCodeConstants.FORBIDDEN);
//        }

        AiAppKnowledgeFile file = new AiAppKnowledgeFile();
        file.setPid(reqVO.getPid());
        file.setType(reqVO.getType());
        file.setFileName(reqVO.getFileName());
        file.setSocialUserId(loginUser.getId());
        file.setSocialType(loginUser.getUserType());
        file.setLevel(level);
        file.setT(Long.valueOf(BaseConstant.ZERO));
        file.setCreatedAt(LocalDateTime.now());
        file.setUpdatedAt(LocalDateTime.now());
        save(file);

        // 如果是文件类型(type=2)，需要通过OpenAPI代理上传图片
        if (BaseConstant.TWO.equals(reqVO.getType())) {
            if (reqVO.getFile() == null) {
                throw exception(ErrorCodeConstants.ERROR_5019);
            }
            String ext = CommonUtil.getFileExtension(reqVO.getFile().getOriginalFilename());
            if (StringUtils.isEmpty(ext)) {
                throw exception(ErrorCodeConstants.ERROR_5025);
            }

            Integer MAX_FILE_SIZE = yudaoSystemService.getUploadLimitSize(ext.toLowerCase());
            if (reqVO.getFile().getSize() > MAX_FILE_SIZE*1024*1024) {
                throw exception(ErrorCodeConstants.ERROR_5026, MAX_FILE_SIZE+"MB");
            }
            try {
                // 调用OpenAPI服务代理上传请求
                Object uploadResult = openApiService.proxyRequest(request, "/upload_to_cos_private");
                log.info("文件上传代理请求完成，结果: {}", uploadResult);
                JSONObject result = JSONObject.parseObject(JSONObject.toJSONString(uploadResult));
                if (result.getJSONObject("data") != null) {
                    file.setCdnSignedUrl(result.getJSONObject("data").getString("cdn_signed_url"));
                    file.setCosFilePath(result.getJSONObject("data").getString("cos_file_path"));
                    file.setFileMd5(result.getJSONObject("data").getString("file_md5"));
                    file.setFileSize(result.getJSONObject("data").getLong("file_size"));
                    file.setT(System.currentTimeMillis()/BaseConstant.KILO + 1800);
                    updateById(file);
                    doChatMessage(file.getId(), reqVO.getFile(), loginUser.getUserType(), loginUser.getId());
                } else {
                    throw exception(ErrorCodeConstants.ERROR_5038);
                }
            } catch (Exception e) {
                log.error("文件上传失败，错误信息: {}", e);
                throw exception(ErrorCodeConstants.ERROR_5038);
            }
        }

        return toBean(file);
    }

    @Async
    private void doChatMessage(Integer id, MultipartFile file, Integer socialType, Long socialUserId) {
        try {
            List<KnowledgeResponse> dirs = new ArrayList<>();
            recursiveSelectParents(dirs,  id);
            Boolean isDataScore = dirs.stream().anyMatch(dir -> dir.getFileName().equals(DIR_NAME) && dir.getPid().equals(BaseConstant.ZERO));
            if (!isDataScore) {
                // 不是顶层目录下的DataScore目录，不需要处理
                return;
            }

            MedsciUsers user = medsciUsersService.getBySocialUserId(socialType, socialUserId);
            String appId = getAppId();
            FilesUploadRequest req = new FilesUploadRequest();
            req.setFile(file);
            req.setAppId(appId);
            req.setUser(user.getUserName());
            JSONObject uploadRes = apiTokensService.filesUpload(req);
            JSONArray files = new JSONArray();
            files.add(uploadRes);

            ChatMessagesRequest chatReq = new ChatMessagesRequest();
            chatReq.setAppId(appId);
            chatReq.setUser(user.getUserName());
            chatReq.setQuery("请评估上传的附件");
            chatReq.setFiles(files);
            chatReq.setInputs(new JSONObject());
            JSONObject chatRes = apiTokensService.chatMsg1(chatReq);
            AiAppKnowledgeFile knowledgeFile = new AiAppKnowledgeFile();
            knowledgeFile.setId(id);
            knowledgeFile.setDifyInfo(uploadRes.toJSONString());
            knowledgeFile.setDifyRes(chatRes.toJSONString());
            updateById(knowledgeFile);
        } catch (Exception e) {
            log.error("文件上传dify失败，错误信息: {}", e);
        }
    }

    @Override
    public List<KnowledgeResponse> getFilePath(Integer id) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        AiAppKnowledgeFile file = getById(id);
        if (file == null || !file.getSocialUserId().equals(loginUser.getId()) || !file.getSocialType().equals(loginUser.getUserType())) {
            throw exception(ErrorCodeConstants.FORBIDDEN);
        }

        List<KnowledgeResponse> res = new ArrayList<>();
        res.add(toBean(file));
        // 递归查询所有父节点
        recursiveSelectParents(res, file.getPid());

        return res.stream().sorted((o1, o2) -> o1.getLevel().compareTo(o2.getLevel())).collect(Collectors.toList());
    }

    /**
     * 递归查询父节点
     *
     * @param res 结果列表
     * @param pid 父节点ID
     */
    private void recursiveSelectParents(List<KnowledgeResponse> res, Integer pid) {
        // 查询父节点
        if (pid != null && pid > BaseConstant.ZERO) {
            AiAppKnowledgeFile parent = getById(pid);
            if (parent != null) {
                res.add(toBean(parent));
                // 继续递归查询父节点的父节点
                recursiveSelectParents(res, parent.getPid());
            }
        }
    }

    @Override
    public Boolean delete(Integer id) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        AiAppKnowledgeFile file = getById(id);
        if (file == null) {
            return true;
        }

        if (file.getSocialUserId().equals(loginUser.getId()) && file.getSocialType().equals(loginUser.getUserType())) {
            return removeById(id);
        }

        throw exception(ErrorCodeConstants.FORBIDDEN);
    }

    @Override
    public KnowledgeResponse uploadToCompetitionDir(MultipartFile file, HttpServletRequest request) {
        if (file == null) {
            throw exception(ErrorCodeConstants.ERROR_5019);
        }

        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();

        // 查找或创建"参赛作品"目录
        Integer competitionDirId = getOrCreateCompetitionDir(loginUser);

        // 创建文件上传请求
        FileUploadRequest reqVO = FileUploadRequest.builder()
                .type(BaseConstant.TWO) // 文件类型
                .pid(competitionDirId)  // 父目录ID为参赛作品目录
                .fileName(file.getOriginalFilename())
                .file(file)
                .build();

        return create(reqVO, request);
    }

    /**
     * 获取或创建参赛作品目录
     * @param loginUser 当前登录用户
     * @return 参赛作品目录ID
     */
    private Integer getOrCreateCompetitionDir(LoginUser loginUser) {
        // 查询参赛作品目录
        KnowledgeParam param = KnowledgeParam.builder()
                .socialUserId(loginUser.getId())
                .socialType(loginUser.getUserType())
                .pid(BaseConstant.ZERO)
                .type(BaseConstant.ONE)
                .fileName("参赛作品")
                .build();

        List<AiAppKnowledgeFile> files = selectList(param);

        if (!files.isEmpty()) {
            // 找到现有的参赛作品目录
            return files.get(0).getId();
        }

        // 如果不存在，先确保DataScore目录存在，然后创建参赛作品目录
        initDataScore();

        // 重新查询参赛作品目录
        files = selectList(param);
        if (!files.isEmpty()) {
            return files.get(0).getId();
        }

        throw exception(ErrorCodeConstants.ERROR_5038);
    }

    @Override
    public void downloadFile(Integer id, HttpServletResponse response) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();

        // 验证目录权限
        AiAppKnowledgeFile directory = getById(id);
        if (directory == null || !directory.getSocialUserId().equals(loginUser.getId())
            || !directory.getSocialType().equals(loginUser.getUserType())) {
            throw exception(ErrorCodeConstants.FORBIDDEN);
        }

        // 确保是目录类型
        if (!BaseConstant.ONE.equals(directory.getType())) {
            throw exception(ErrorCodeConstants.BAD_REQUEST);
        }

        try {
            // 递归获取目录下所有文件
            List<AiAppKnowledgeFile> allFiles = getAllFilesRecursively(id, loginUser);

            if (allFiles.isEmpty()) {
                throw exception(ErrorCodeConstants.ERROR_5019);
            }

            // 创建zip文件
            ByteArrayOutputStream zipOutputStream = createZipFromFiles(allFiles, directory.getFileName());

            // 设置响应头
            String zipFileName = directory.getFileName() + "_" + System.currentTimeMillis() + ".zip";
            response.setHeader("Content-Disposition",
                "attachment; filename=" + URLEncoder.encode(zipFileName, StandardCharsets.UTF_8));
            response.setContentType("application/zip");
            response.setContentLength(zipOutputStream.size());

            // 写入响应流
            response.getOutputStream().write(zipOutputStream.toByteArray());
            response.getOutputStream().flush();

            log.info("用户{}成功下载知识库目录: {}, 文件数量: {}", loginUser.getId(), directory.getFileName(), allFiles.size());

        } catch (IOException e) {
            log.error("下载知识库文件失败，目录ID: {}, 错误信息: {}", id, e.getMessage(), e);
            throw exception(ErrorCodeConstants.ERROR_5038);
        } catch (Exception e) {
            log.error("下载知识库文件失败，目录ID: {}, 错误信息: {}", id, e.getMessage(), e);
            throw exception(ErrorCodeConstants.ERROR_5038);
        }
    }