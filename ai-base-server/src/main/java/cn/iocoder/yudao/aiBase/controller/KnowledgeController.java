package cn.iocoder.yudao.aiBase.controller;

import cn.iocoder.yudao.aiBase.dto.param.KnowledgeParam;
import cn.iocoder.yudao.aiBase.dto.request.FileUploadRequest;
import cn.iocoder.yudao.aiBase.dto.response.KnowledgeResponse;
import cn.iocoder.yudao.aiBase.service.AiAppKnowledgeFileService;
import cn.iocoder.yudao.aiBase.service.ApiTokensService;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.enums.GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR;

/**
 * 用户知识库
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/ai-base/knowledge")
@Tag(name = "aiBase-知识库")
public class KnowledgeController {

    @Autowired
    private AiAppKnowledgeFileService aiAppKnowledgeFileService;

    @Autowired
    private ApiTokensService apiTokensService;

    /**
     * 知识库列表
     * @return
     */
    @PostMapping("/getKnowledgeByDir")
    @Operation(summary = "知识库列表")
    public CommonResult<List<KnowledgeResponse>> getKnowledgeByDir(@Valid @RequestBody KnowledgeParam reqVO) {
        try {
            return CommonResult.success(aiAppKnowledgeFileService.getKnowledgeByDir(reqVO));
        } catch (Exception e) {
            e.printStackTrace();
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
        }
    }

    @PostMapping(value = "/create", consumes = "multipart/form-data")
    @Operation(summary = "创建知识库")
    public CommonResult<KnowledgeResponse> create(@Valid FileUploadRequest reqVO,
                                                  HttpServletRequest request) {
        try {
            apiTokensService.getToken(aiAppKnowledgeFileService.getAppId());
            return CommonResult.success(aiAppKnowledgeFileService.create(reqVO, request));
        } catch (Exception e) {
            e.printStackTrace();
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
        }
    }

    @PostMapping("/updateFileName")
    @Operation(summary = "更新文件名")
    public CommonResult<Boolean> updateFileName(@Valid @RequestBody KnowledgeParam reqVO) {
        try {
            return CommonResult.success(aiAppKnowledgeFileService.updateFileName(reqVO.getId(), reqVO.getFileName()));
        } catch (Exception e) {
            e.printStackTrace();
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
        }
    }

    @PostMapping("/getFilePath")
    @Operation(summary = "获取文件路径")
    public CommonResult<List<KnowledgeResponse>> getFilePath(@RequestParam("id") Integer id) {
        try {
            return CommonResult.success(aiAppKnowledgeFileService.getFilePath(id));
        } catch (Exception e) {
            e.printStackTrace();
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
        }
    }

    @PostMapping("/delete")
    @Operation(summary = "删除知识库")
    public CommonResult<Boolean> delete(@RequestParam("id") Integer id) {
        try {
            return CommonResult.success(aiAppKnowledgeFileService.delete(id));
        } catch (Exception e) {
            e.printStackTrace();
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
        }
    }

    @PostMapping(value = "/uploadToCompetition", consumes = "multipart/form-data")
    @Operation(summary = "直接上传文件到参赛作品目录")
    public CommonResult<KnowledgeResponse> uploadToCompetition(@RequestParam("file") MultipartFile file,
                                                               HttpServletRequest request) {
        try {
            return CommonResult.success(aiAppKnowledgeFileService.uploadToCompetitionDir(file, request));
        } catch (Exception e) {
            e.printStackTrace();
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
        }
    }

}
